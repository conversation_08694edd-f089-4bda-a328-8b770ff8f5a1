# 禁用 zsh 权限检查警告
ZSH_DISABLE_COMPFIX=true

# 跳过不安全目录检查
skip_global_compinit=1

# 手动初始化补全系统（忽略权限检查）
autoload -U compinit
compinit -u

# 智谱 GLM-4.5 API 配置
export ANTHROPIC_BASE_URL="https://open.bigmodel.cn/api/anthropic"
export ANTHROPIC_AUTH_TOKEN="79d71402aeda42878f3808c503b34a20.0a8JaXL28yJUq1WT"

# nvm 配置
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# 添加 npm-global 到 PATH
export PATH="$HOME/.npm-global/bin:$PATH"
